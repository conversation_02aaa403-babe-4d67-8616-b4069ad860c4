from __future__ import annotations

from typing import Iterable

from packing.protocols import Box3D
from packing.build import build_packer_from_toml
from packing.visualizer_o3d import Open3DVisualizer
from packing.streams import FixedListStream, RandomUniformStream, CyclicSetStream


def generate_dummy_boxes() -> Iterable[Box3D]:
    # You can change this to try different distributions/generators
    yield from [
        Box3D(0.3, 0.2, 0.1),
        Box3D(0.3, 0.2, 0.1),
        Box3D(0.2, 0.2, 0.1),
        Box3D(0.2, 0.3, 0.2),
        Box3D(0.25, 0.25, 0.2),
        Box3D(0.15, 0.15, 0.1),
    ]


if __name__ == "__main__":
    USE_PYBULLET_GUI = False  # set True to see PyBullet GUI
    packer = build_packer_from_toml("config/packing.toml", use_gui=USE_PYBULLET_GUI)

    ## Choose stream mode
    # stream = FixedListStream(list(generate_dummy_boxes()))
    # stream = RandomUniformStream(
    #     count=20, w_range=(0.1, 0.3), d_range=(0.1, 0.3), h_range=(0.1, 0.3), seed=42
    # )
    stream = CyclicSetStream(set_boxes=list(generate_dummy_boxes())[:3], repeat=11)

    ## Choose visualizer
    # viz = None
    viz = Open3DVisualizer(show_window=True)

    result = packer.pack_stream(stream.stream(), visualizer=viz)
    print(f"Placed {len(result.placements)} boxes; volume {result.volume_filled_pct:.2f}%")
    for i, pb in enumerate(result.placements):
        print(
            f"{i:02d} -> pose=({pb.pose.x:.3f},{pb.pose.y:.3f},{pb.pose.z:.3f},{pb.pose.yaw:.3f}) size=({pb.box.width:.3f},{pb.box.depth:.3f},{pb.box.height:.3f})"
        )
    # Block on final view if visualizer is enabled
    if viz is not None:
        viz.block_until_closed(packer.crate, packer.placed)
