# Minimal Installation Guide for FRAMOS D400e Python Bindings

This guide provides a minimal set of instructions to get the FRAMOS D400e Python bindings working on a fresh Ubuntu system.

## 1. Prerequisites

- **Ubuntu 20.04, 22.04, or 24.04 LTS**
- **FRAMOS D400e Software Package:** Downloaded and extracted.

## 2. Install FRAMOS Software

First, install the required `.deb` packages from the FRAMOS software package.

```bash
# Navigate to your FRAMOS package directory
cd /path/to/your/FRAMOS_D400e_Software_Package

# Update package lists
sudo apt-get update

# Install kernel headers (skip on Jetson)
sudo apt-get install -y linux-headers-$(uname -r)

# Install FRAMOS CameraSuite
sudo apt install -y ./FRAMOS_CameraSuite*.deb

# On Ubuntu 22/24, install libssl1.1
wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
sudo dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb

# Install FRAMOS librealsense2
sudo apt install -y ./FRAMOS-librealsense2*.deb

# Reboot the system
sudo reboot
```

## 3. Verify Installation

Verify that the camera is detected by the system.


## 4.Install Python Bindings

After rebooting, build the Python bindings from the sources installed by the `.deb` package. You can also use the whl file called `pyrealsense2_framos_d400e-2.55.10-py3-none-any.whl` on [Google Drive](https://drive.google.com/file/d/1KEKlHjGSSGJzFXIclZilsORJUpcF13ku) instead. Place it in the infra/framos folder before doing `pipenv install` in the code directory. If you succeed you can skip step 5 and 6.

## 5. Build and Install Python Bindings from Source

```bash
# Install build dependencies
sudo apt install -y build-essential cmake libglfw3-dev libgtk-3-dev git libssl-dev libusb-1.0-0-dev pkg-config python3-dev

# Copy the sources to your home directory
cp -r /usr/src/librealsense2 ~

# Create a build directory
cd ~/librealsense2
mkdir build && cd build

# Configure the build with Python bindings enabled
cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_PYTHON_BINDINGS:bool=true

# Build the Python bindings
make pyrealsense2 -j$(nproc)

# Install the bindings for all users
sudo make install
```

## 6. Install with Pipenv

If you use Pipenv, you can install the bindings into your project's virtual environment.

```bash
# Navigate to your project directory
cd /path/to/your/project

# Get the path to your virtual environment
venv_path=$(pipenv --venv)

# Copy the Python bindings to the virtual environment
cp ~/librealsense2/build/pyrealsense2.cpython-*-x86_64-linux-gnu.so* $venv_path/lib/python*/site-packages/
```

## 7. Verify the Installation

Create a Python script `test_camera.py` with the following content:

```python
import pyrealsense2 as rs

try:
    ctx = rs.context()
    devices = ctx.query_devices()
    if len(devices) > 0:
        print(f"Found {len(devices)} RealSense devices.")
        for dev in devices:
            print(f"  - {dev.get_info(rs.camera_info.name)}")
    else:
        print("No RealSense devices found.")
except Exception as e:
    print(f"An error occurred: {e}")
```

Run the script:

```bash
# For system-wide installation
python3 test_camera.py

# For Pipenv installation
pipenv run python test_camera.py
```

You should see your connected FRAMOS D400e camera listed in the output.

