import math
import pytest

from packing.protocols import Box3<PERSON>, Po<PERSON>, PlacedBox, Crate
from packing.constraints import InsideCrateConstraint, NoOverlapConstraint, VerticalPathClearConstraint


def test_inside_crate_basic():
    crate = Crate(width=1.0, depth=1.0, max_height=1.0)
    box = Box3D(0.2, 0.3, 0.4)
    candidate = PlacedBox(box, Pose(x=0.1, y=0.1, z=0.0, yaw=0.0))
    assert InsideCrateConstraint().check(crate, candidate, [], None)


def test_inside_crate_out_of_bounds():
    crate = Crate(width=1.0, depth=1.0, max_height=1.0)
    box = Box3D(0.6, 0.6, 0.6)
    candidate = PlacedBox(box, Pose(x=0.6, y=0.6, z=0.0, yaw=0.0))
    assert not InsideCrateConstraint().check(crate, candidate, [], None)


def test_no_overlap_rotated_footprints():
    crate = Crate(width=1.0, depth=1.0, max_height=1.0)
    a = PlacedBox(Box3D(0.3, 0.2, 0.1), Pose(x=0.0, y=0.0, z=0.0, yaw=0.0))
    b = PlacedBox(Box3D(0.3, 0.2, 0.1), Pose(x=0.3, y=0.0, z=0.0, yaw=math.pi/2))
    assert NoOverlapConstraint().check(crate, b, [a], None)


def test_vertical_clear_path():
    # Constraint itself uses placed boxes to check path; no simulator required
    crate = Crate(width=1.0, depth=1.0, max_height=1.0)
    placed = [PlacedBox(Box3D(0.3, 0.3, 0.2), Pose(0.0, 0.0, 0.0, 0.0))]
    cand = PlacedBox(Box3D(0.3, 0.3, 0.2), Pose(0.0, 0.0, 0.1, 0.0))
    assert not VerticalPathClearConstraint().check(crate, cand, placed, None)

    cand2 = PlacedBox(Box3D(0.3, 0.3, 0.2), Pose(0.0, 0.3, 0.1, 0.0))
    assert VerticalPathClearConstraint().check(crate, cand2, placed, None)

