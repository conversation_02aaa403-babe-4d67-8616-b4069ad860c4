from __future__ import annotations

from typing import Sequence

import tomlkit

from .constraints import InsideCrateConstraint, NoOverlapConstraint, VerticalPathClearConstraint
from .heuristics import NeighborCornersHeuristic
from .scores.gap_score import GapScore
from .scores.height_score import HeightScore
from .scores.skew_score import SkewScore
from .scores.volume_bias_score import VolumeBiasScore
from .scores.overhang_score import OverhangScore
from .packer import Packer
from .protocols import Crate
from .simulator_bullet import BulletPhysicsSimulator


def build_packer_from_toml(path: str = "config/packing.toml", use_gui: bool = False) -> Packer:
    with open(path, "r") as f:
        cfg = tomlkit.load(f)
    crate = Crate(
        width=float(cfg["crate"]["width"]),
        depth=float(cfg["crate"]["depth"]),
        max_height=float(cfg["crate"]["max_height"]),
    )
    sim_cfg = cfg["simulator"]
    sim = BulletPhysicsSimulator(
        time_step=float(sim_cfg.get("step_time", 1.0 / 120.0)),
        gravity_z=float(sim_cfg.get("gravity_z", -5.0)),
        drop_mass=float(sim_cfg.get("mass", 5.0)),
        stabilize_lin_vel=float(sim_cfg.get("stabilize_lin_vel", 0.01)),
        stabilize_ang_vel=float(sim_cfg.get("stabilize_ang_vel", 0.01)),
        max_steps_default=int(sim_cfg.get("max_steps", 180)),
        start_clearance=float(sim_cfg.get("start_clearance", 0.05)),
        wall_margin=float(sim_cfg.get("wall_margin", 0.10001)),
        use_gui=use_gui,
    )
    placement = NeighborCornersHeuristic(
        neighbors=int(cfg["placement"].get("neighbors", 5)),
        distance_between=float(cfg["placement"].get("distance_between", 0.015)),
    )
    scoring = [
        HeightScore(weight=float(cfg["scoring"].get("factor_height", 30.0))),
        GapScore(
            weight=float(cfg["scoring"].get("factor_gaps", 30.0)),
            target_gap=float(cfg["placement"].get("distance_between", 0.015)),
        ),
        SkewScore(
            weight=float(cfg["scoring"].get("factor_skewed", 4.0)),
            max_skew_deg=float(cfg["scoring"].get("max_skew_deg", 10.0)),
        ),
        VolumeBiasScore(weight=float(cfg["scoring"].get("factor_volume", 0.0))),
        OverhangScore(
            weight=float(cfg["scoring"].get("factor_overhang", 10.0)),
            grid_resolution=float(cfg["scoring"].get("overhang_grid_resolution", 0.02)),
        ),
    ]

    yaw_candidates: Sequence[float] = list(cfg["placement"].get("yaw_candidates_deg", [0, 90]))
    buffer_size = int(cfg["buffer"].get("size", 4))
    packer = Packer(
        crate=crate,
        simulator=sim,
        placement_heuristic=placement,
        scoring_heuristics=scoring,
        yaw_candidates_deg=yaw_candidates,
        buffer_size=buffer_size,
    )
    return packer
