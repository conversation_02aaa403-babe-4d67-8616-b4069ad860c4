# Conveyor Control System

This Arduino script controls a 4-part conveyor system for the Bleckmann Box Stacking project.

## System Overview

The conveyor system consists of 4 sequential parts, each with:
- A sensor at the end to detect boxes
- A motor to drive the conveyor belt
- An LED indicator to show motor status

## Hardware Requirements

### Components
- Arduino-compatible microcontroller (Arduino Uno, Nano, or similar)
- 4x Proximity sensors (digital output)
- 4x DC motors or motor drivers
- 4x LEDs for status indication
- 4x Current limiting resistors for LEDs (220Ω recommended)
- Emergency stop button (optional)

### Wiring Diagram

```
Sensors (Digital Input with Pullup):
- Part 1 Sensor → Pin 2
- Part 2 Sensor → Pin 3
- Part 3 Sensor → Pin 4
- Part 4 Sensor → Pin 5

Motors (Digital Output):
- Part 1 Motor → Pin 6
- Part 2 Motor → Pin 7
- Part 3 Motor → Pin 8
- Part 4 Motor → Pin 9

Status LEDs (Digital Output):
- Part 1 LED → Pin 10
- Part 2 LED → Pin 11
- Part 3 LED → Pin 12
- Part 4 LED → Pin 13

Optional:
- Emergency Stop → Pin A0
```

## Logic Description

### Box Transfer Rules
1. A box moves from one part to the next only if:
   - The conveyor lock is acquired
   - The current part has a box (sensor blocked)
   - The next part is free (sensor not blocked)
   - Both parts are running

2. Motor control logic:
   - **Part 1**: Runs when there's a box OR when Part 2 is free and ready to receive
   - **Parts 2-3**: Run when they have a box and the next part is free, OR when receiving a box from the previous part
   - **Part 4**: Only runs when EJECT command is received and there's a box present

### Lock System
The conveyor system implements a lock mechanism for safe operation:
- **No movement without lock**: All conveyors remain stopped until lock is acquired
- **Graceful release**: When releasing lock while motors are running, system stops all motors first
- **Safety**: All operations (including EJECT) require lock to be acquired

### Eject System
Part 4 (last conveyor) implements controlled ejection:
- Boxes reaching Part 4 will stop and wait
- Only moves when EJECT command is received
- Prevents multiple boxes from accumulating on Part 4
- Ensures one box at a time processing

### Safety Features
- Sensor debouncing to prevent false triggers
- Emergency stop functionality
- Maximum continuous run time protection
- Serial command interface for debugging

## Installation

1. Copy the files to your Arduino IDE workspace
2. Open `conveyor_control.ino` in Arduino IDE
3. Verify pin assignments in `config.h` match your hardware
4. Upload to your Arduino board

## Configuration

Edit `config.h` to customize:
- Pin assignments
- Timing parameters
- Safety settings
- Debug options

## Serial Commands

Connect to the Arduino via Serial Monitor (9600 baud) and use these commands:

### Lock Control Commands
- `ACQUIRE` - Acquire conveyor lock (required for any movement)
  - Response: `LOCK_ACQUIRED` or `LOCK_ALREADY_ACQUIRED`
- `RELEASE` - Release conveyor lock
  - Response: `RELEASING_LOCK` (if motors running) then `RELEASED_LOCK`
  - Response: `RELEASED_LOCK` (if no motors running)
  - Response: `LOCK_NOT_ACQUIRED` (if lock not held)

### Eject Control Commands
- `EJECT` - Eject box from Part 4 (requires lock to be acquired)
  - Response: `EJECT_SUCCESS` (when box successfully ejected)
  - Response: `EJECT_NO_BOX_PRESENT` (if no box on Part 4)
  - Response: `EJECT_IN_PROGRESS` (if eject already in progress)
  - Response: `EJECT_LOCK_NOT_ACQUIRED` (if lock not acquired)

### System Control Commands
- `STOP` - Emergency stop all motors
- `STATUS` - Print current system status
- `RESET` - Reset the system (clears all states)

## Troubleshooting

### Common Issues
1. **Motors not starting**: Check wiring and power supply
2. **False sensor triggers**: Adjust `DEBOUNCE_DELAY` in config.h
3. **Boxes getting stuck**: Verify sensor positioning and timing
4. **System not responding**: Check serial connection and baud rate

### Debug Mode
Enable `DEBUG_MODE` in config.h for detailed logging of:
- Sensor state changes
- Motor state changes
- Box transfer events

## Testing

### Basic Hardware Tests
1. **Sensor Test**: Place objects at sensor locations and verify detection
2. **Motor Test**: Manually trigger sensors and verify motor responses
3. **Emergency Stop Test**: Verify emergency stop functionality

### System Operation Tests
1. **Lock Test**:
   - Send `ACQUIRE` and verify `LOCK_ACQUIRED` response
   - Verify conveyors can now operate
   - Send `RELEASE` and verify proper shutdown sequence
2. **Transfer Test**:
   - Acquire lock first
   - Place boxes and verify proper transfer between Parts 1-3
   - Verify boxes stop at Part 4 and wait for eject command
3. **Eject Test**:
   - Place box on Part 4
   - Send `EJECT` command and verify `EJECT_SUCCESS` response
   - Test `EJECT_NO_BOX_PRESENT` response when Part 4 is empty
4. **Safety Test**:
   - Verify no movement without lock
   - Test graceful shutdown when releasing lock while motors running

## Maintenance

- Regularly clean sensors to prevent false readings
- Check motor connections for loose wires
- Monitor system logs for unusual behavior
- Test emergency stop functionality periodically

## Operation Sequence

### Typical Operation Flow
1. **System Startup**: All motors stopped, lock not acquired
2. **Acquire Lock**: Send `ACQUIRE` command → Response: `LOCK_ACQUIRED`
3. **Normal Operation**: Boxes move through Parts 1-3 automatically
4. **Box Reaches Part 4**: Box stops and waits for eject command
5. **Eject Box**: Send `EJECT` command → Response: `EJECT_SUCCESS`
6. **Release Lock**: Send `RELEASE` command → Response: `RELEASING_LOCK` then `RELEASED_LOCK`

### Error Handling
- Always check command responses for error conditions
- Acquire lock before attempting any operations
- Handle `EJECT_NO_BOX_PRESENT` by checking Part 4 status
- Monitor for `RELEASING_LOCK` state during shutdown
