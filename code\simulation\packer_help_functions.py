import random
import pybullet as p
import cv2 as cv
import numpy as np
from box import Box
from pallet import <PERSON><PERSON><PERSON>


def take_screenshot(name: str, pallet:Palle<PERSON>):

    # Initialise Vieuw
    cameraDistance = 3
    cameraYaw = 50  
    cameraPitch = -50  
    cameraTargetPosition = [pallet.width/2, pallet.depth/2, pallet.height*2] 
    # Vieuw matrix for screenshot
    viewMatrix = p.computeViewMatrixFromYawPitchRoll(
        cameraTargetPosition=cameraTargetPosition,
        distance=cameraDistance,
        yaw=cameraYaw,
        pitch=cameraPitch,
        roll=0,
        upAxisIndex=2)
    img_width = 640
    img_height = 480
    # Projection matrix for screenshot
    projectionMatrix = p.computeProjectionMatrixFOV(
        fov=60,  # Field of view in degrees
        aspect= img_width / img_height,
        nearVal=0.1,
        farVal=100.0)
    # Make screenshot of stable boxes using projection and vieuw matrix
    width, height, rgbImg, depthImg, segImg = p.getCameraImage(
        width=img_width,
        height=img_height,
        viewMatrix=viewMatrix,
        projectionMatrix=projectionMatrix,
        renderer=p.ER_BULLET_HARDWARE_OPENGL)
    # Convert img to BGR format and save with OpenCV
    # PyBullet returns RGBA as a tuple/list, need to convert properly

    # Convert to numpy array and reshape to image format
    rgb_array = np.array(rgbImg, dtype=np.uint8)

    # Reshape from flattened array to (height, width, channels)
    # PyBullet returns RGBA, so 4 channels
    rgb_array = rgb_array.reshape((img_height, img_width, 4))

    # Remove alpha channel to get RGB
    rgb_array = rgb_array[:, :, :3]

    # Convert RGB to BGR for OpenCV
    bgr_img = cv.cvtColor(rgb_array, cv.COLOR_RGB2BGR)
    cv.imwrite(f"{name}.png", bgr_img)

def create_random_boxes(nb_boxes, xrange, yrange, zrange, int=True):
    boxes = []

    for i in range(nb_boxes):
        if int:
            width = random.randint(xrange[0],xrange[1])
            depth = random.randint(yrange[0], yrange[1])
            height = random.randint(zrange[0], zrange[1])
        else:
            width = random.uniform(xrange[0], xrange[1])
            depth = random.uniform(yrange[0], yrange[1])
            height = random.uniform(zrange[0], zrange[1])

        new_box = Box(width,depth,height)
        boxes.append(new_box)
    
    return boxes

def create_random_from_set(nb_boxes):
    # Define the set of predefined boxes
    box1 = Box(0.1, 0.1, 0.1)
    box2 = Box(0.3, 0.2, 0.2)
    box3 = Box(0.3, 0.1, 0.1)
    box4 = Box(0.2, 0.2, 0.2)
    box5 = Box(0.1, 0.3, 0.1)
    box6 = Box(0.3, 0.2, 0.1)

    # Store the predefined boxes in a list
    predefined_boxes = [box1, box2, box3, box4, box5, box6]

    # Generate the list of random boxes
    boxes = []
    for _ in range(nb_boxes):
        # Randomly select a box from the predefined list
        random_box = random.choice(predefined_boxes)
        # Create a new instance (copy) of the box
        new_box = Box(random_box.width, random_box.depth, random_box.height)
        boxes.append(new_box)
    
    return boxes




