from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable
import math

from .protocols import Box3D, ConstraintProtocol, Crate, PlacedBox, PhysicsSimulatorProtocol


def _footprint_dims(box: Box3D, yaw: float) -> tuple[float, float]:
    """Return effective axis-aligned footprint dims (x_size, y_size) for yaw (0 or 90 deg)."""
    # Decide swap by dominant axis of rotation
    if abs(math.cos(yaw)) >= abs(math.sin(yaw)):
        return box.width, box.depth
    else:
        return box.depth, box.width


@dataclass
class InsideCrateConstraint(ConstraintProtocol):
    """Ensure the placed footprint and height are within the crate."""

    def check(
        self,
        crate: Crate,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
        simulator: PhysicsSimulatorProtocol | None = None,
    ) -> bool:
        eff_w, eff_d = _footprint_dims(candidate.box, candidate.pose.yaw)
        x_ok = 0.0 <= candidate.pose.x <= crate.width - eff_w + 1e-6
        y_ok = 0.0 <= candidate.pose.y <= crate.depth - eff_d + 1e-6
        z_ok = (candidate.pose.z + candidate.box.height) <= crate.max_height + 1e-6
        return x_ok and y_ok and z_ok


@dataclass
class NoOverlapConstraint(ConstraintProtocol):
    """Ensure axis-aligned no-overlap with already placed boxes (final pose).

    Allows a small epsilon to tolerate numeric jitter and enable stacking when z separates volumes.
    """

    eps: float = 2e-3

    def check(
        self,
        crate: Crate,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
        simulator: PhysicsSimulatorProtocol | None = None,
    ) -> bool:
        eff_w, eff_d = _footprint_dims(candidate.box, candidate.pose.yaw)
        c_min_x, c_max_x = candidate.pose.x, candidate.pose.x + eff_w
        c_min_y, c_max_y = candidate.pose.y, candidate.pose.y + eff_d
        c_min_z, c_max_z = candidate.pose.z, candidate.pose.z + candidate.box.height
        for pb in placed:
            p_eff_w, p_eff_d = _footprint_dims(pb.box, pb.pose.yaw)
            p_min_x, p_max_x = pb.pose.x, pb.pose.x + p_eff_w
            p_min_y, p_max_y = pb.pose.y, pb.pose.y + p_eff_d
            p_min_z, p_max_z = pb.pose.z, pb.pose.z + pb.box.height
            # Consider overlapping only if intervals overlap by more than eps on all axes
            overlap = (
                (c_min_x < p_max_x - self.eps)
                and (p_min_x < c_max_x - self.eps)
                and (c_min_y < p_max_y - self.eps)
                and (p_min_y < c_max_y - self.eps)
                and (c_min_z < p_max_z - self.eps)
                and (p_min_z < c_max_z - self.eps)
            )
            if overlap:
                return False
        return True


@dataclass
class VerticalPathClearConstraint(ConstraintProtocol):
    """Conservative vertical descent clearance check using AABB column test.

    Ensures any static box overlapping the XY footprint has top_z <= candidate bottom_z.
    """

    def check(
        self,
        crate: Crate,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
        simulator: PhysicsSimulatorProtocol | None = None,
    ) -> bool:
        eff_w, eff_d = _footprint_dims(candidate.box, candidate.pose.yaw)
        c_min_x, c_max_x = candidate.pose.x, candidate.pose.x + eff_w
        c_min_y, c_max_y = candidate.pose.y, candidate.pose.y + eff_d
        c_bottom = candidate.pose.z
        for pb in placed:
            p_min_x, p_max_x = pb.pose.x, pb.pose.x + pb.box.width
            p_min_y, p_max_y = pb.pose.y, pb.pose.y + pb.box.depth
            overlap_x = (c_min_x < p_max_x) and (p_min_x < c_max_x)
            overlap_y = (c_min_y < p_max_y) and (p_min_y < c_max_y)
            if overlap_x and overlap_y:
                p_top = pb.pose.z + pb.box.height
                if p_top > c_bottom + 1e-4:
                    return False
        return True
