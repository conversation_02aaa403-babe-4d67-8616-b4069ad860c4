from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable, List, Tuple

import math

from .protocols import (
    Box3D,
    Crate,
    PlacementHeuristicProtocol,
    PlacedBox,
    Pose,
    PhysicsSimulatorProtocol,
    ScoringHeuristicProtocol,
)


@dataclass
class NeighborCornersHeuristic(PlacementHeuristicProtocol):
    neighbors: int = 5
    distance_between: float = 0.015

    def propose(
        self, crate: Crate, box: Box3D, placed: Iterable[PlacedBox], max_candidates: int
    ) -> Iterable[PlacedBox]:
        candidate_coords: List[Tuple[float, float, float]] = []
        # Always include crate corners
        corners = [
            (0.0, 0.0, 0.0),
            (crate.width - box.width, 0.0, 0.0),
            (0.0, crate.depth - box.depth, 0.0),
            (crate.width - box.width, crate.depth - box.depth, 0.0),
        ]
        candidate_coords.extend(corners)
        # Use last N placed boxes to propose aligned positions around them
        recent = list(placed)[-self.neighbors :]
        for pb in recent:
            x, y = pb.pose.x, pb.pose.y
            w, d = pb.box.width, pb.box.depth
            b_w, b_d = box.width, box.depth
            db = self.distance_between
            # align corners on top footprint
            candidate_coords.extend(
                [
                    (x, y, 0.0),
                    (x + w - b_w, y, 0.0),
                    (x, y + d - b_d, 0.0),
                    (x + w - b_w, y + d - b_d, 0.0),
                ]
            )
            # around footprint with small spacing
            candidate_coords.extend(
                [
                    (x, y - b_d - db, 0.0),
                    (x + w - b_w, y - b_d - db, 0.0),
                    (x + w + db, y, 0.0),
                    (x + w + db, y + d - b_d, 0.0),
                    (x, y + d + db, 0.0),
                    (x + w - b_w, y + d + db, 0.0),
                    (x - b_w - db, y, 0.0),
                    (x - b_w - db, y + d - b_d, 0.0),
                ]
            )
        # Deduplicate and limit
        seen = set()
        uniq_coords = []
        for c in candidate_coords:
            key = (round(c[0], 4), round(c[1], 4), round(c[2], 4))
            if key not in seen:
                seen.add(key)
                uniq_coords.append(c)

        # Convert coordinate tuples to PlacedBox instances
        candidates: List[PlacedBox] = []
        for x, y, z in uniq_coords[:max_candidates]:
            pose = Pose(x=x, y=y, z=z, yaw=0.0)
            candidates.append(PlacedBox(box=box, pose=pose))

        return candidates


# Scoring heuristics are in separate classes for injection
