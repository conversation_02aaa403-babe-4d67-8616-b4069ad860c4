from __future__ import annotations

from dataclasses import dataclass
from typing import Generator, Iterable, Optional

import numpy as np

from box_top.box_top_detector_protocol import BoxDetectorProtocol
from .protocols import Box3D, PackerInputProtocol


@dataclass
class BoxDetectorInput(PackerInputProtocol):
    """Adapter that turns a BoxDetectorProtocol into a stream of Box3D.

    Assumes ConveyorBox.extent is (width, depth, height).
    """

    detector: BoxDetectorProtocol

    def stream(self) -> Generator[Box3D, None, None]:
        for cb in self.detector.detect_boxes():  # type: ignore[attr-defined]
            if cb is None:
                continue
            w, d, h = float(cb.extent[0]), float(cb.extent[1]), float(cb.extent[2])
            yield Box3D(width=w, depth=d, height=h)


@dataclass
class ListInput(PackerInputProtocol):
    boxes: Iterable[Box3D]

    def stream(self) -> Generator[Box3D, None, None]:
        for b in self.boxes:
            yield b

