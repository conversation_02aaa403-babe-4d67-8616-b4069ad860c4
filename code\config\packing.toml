# Packing configuration

[buffer]
size = 4

[simulator]
engine = "bullet"        # currently only 'bullet' implemented
mass = 5.0               # mass used during drop simulations
step_time = 0.008333333  # 1/120
max_steps = 180          # max simulation steps per drop
stabilize_lin_vel = 0.01
stabilize_ang_vel = 0.01
start_clearance = 0.05   # meters above target bottom to spawn dynamic box
wall_margin = 0.10001    # wall offset thickness
gravity_z = -5.0         # gravity acceleration (m/s^2); e.g., -9.81 for Earth


[crate]
# Pallet/crate dimensions in meters
width = 1.22
depth = 1.27
max_height = 1.10

[placement]
# Minimum clearance between boxes and walls (in meters)
distance_between = 0.015
# Candidate yaw angles in degrees (Z-axis only)
yaw_candidates_deg = [0, 90]
# How many neighbors to use for proposing candidate positions
neighbors = 5
# Total max candidate drop positions per box
max_candidates = 80

[scoring]
# Weighted combination of multiple heuristics
factor_height = 30.0
factor_gaps = 30.0
factor_skewed = 4.0
factor_volume = 0.0
max_skew_deg = 10.0
# Overhang scoring
factor_overhang = 10.0
overhang_grid_resolution = 0.02
