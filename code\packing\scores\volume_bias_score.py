from dataclasses import dataclass
from typing import Iterable

from ..protocols import Crate, PhysicsSimulatorProtocol, PlacedBox, ScoringHeuristicProtocol


@dataclass
class VolumeBiasScore(ScoringHeuristicProtocol):
    weight: float = 1.0

    def score(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
    ) -> float:
        return self.weight * (candidate.box.width + candidate.box.depth + candidate.box.height)
