from functools import cache
import cv2 as cv


@cache
def get_detector():
    aruco_dict = cv.aruco.getPredefinedDictionary(cv.aruco.DICT_4X4_50)
    parameters = cv.aruco.DetectorParameters()

    detector = cv.aruco.ArucoDetector(aruco_dict, parameters)
    return detector


def detect_aruco(img):
    gray = cv.cvtColor(img, cv.COLOR_BGR2GRAY)
    detector = get_detector()

    corners, ids, _rejectedImgPoints = detector.detectMarkers(gray)
    return corners, ids
