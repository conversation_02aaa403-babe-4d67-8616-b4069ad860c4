from typing import Generator, Optional

from loguru import logger
import numpy as np
import tomlkit

from box_top import ConveyorBox
from box_top.box_top_detector_protocol import BoxTopDetectorProtocol


class BoxDetector:
    """
    Box detector that uses a BoxTop stream from a box top detector.

    The algorithm is simple:
    - If a BoxTop is detected with center x >= scan_line, it gets yielded
    - Then it waits for a BoxTop with center x < scan_line - reset_offset before yielding the next box
    """

    def __init__(self, box_top_detector: BoxTopDetectorProtocol, reset_offset: float = 0.02):
        """
        Initialize the BoxDetector with a box top detector.

        Args:
            box_top_detector: Box top detector implementation following BoxTopDetectorProtocol
            reset_offset: Offset below scan line that the center x must reach before reset (prevents false positives)
        """
        self.box_top_detector = box_top_detector
        self.reset_offset = reset_offset
        self._waiting_for_reset = True

        with open("config/conveyor.toml", "r") as f:
            config = tomlkit.load(f)
        # Transform the origin to get the scan line x coordinate.
        self.scan_line = config["calibration"]["transform"].unwrap()[0][3]  # type: ignore

    def detect_boxes(self) -> Generator[Optional[ConveyorBox], None, None]:
        """
        Start box detection and yield ConveyorBox objects as a stream.
        Only yields boxes when they cross the detection threshold.

        Yields:
            ConveyorBox objects or None if detection fails
        """
        logger.trace("Starting box detection")
        self._waiting_for_reset = True

        for box_top in self.box_top_detector.detect_box_tops():
            if box_top is None:
                continue

            center_x = box_top.center[0]
            logger.trace(f"BoxTop center x: {center_x:.3f}")

            if self._waiting_for_reset:
                # We're waiting for center x < scan_line-reset_offset to reset the detection state
                reset_threshold = self.scan_line - self.reset_offset
                if center_x < reset_threshold:
                    logger.trace(
                        f"Reset condition met (center x {center_x:.3f} < {reset_threshold:.3f}), ready for next box"
                    )
                    self._waiting_for_reset = False
            else:
                # We're ready to detect a new box
                if center_x >= self.scan_line:
                    logger.trace(f"Box detected! Center x: {center_x:.3f}")
                    self._waiting_for_reset = True

                    extent_with_height = np.array(
                        [
                            box_top.extent[0],
                            box_top.extent[1],
                            box_top.extent[2] + box_top.center[2],
                        ]
                    )
                    conveyor_box = ConveyorBox(
                        y_position=box_top.center[1],
                        extent=extent_with_height,
                        rotation=box_top.rotation,
                        volume=box_top.volume,
                    )
                    yield conveyor_box


if __name__ == "__main__":
    # Example usage with mock detector
    from pprint import pprint
    from box_top.box_top_detector_mock import BoxTopDetectorMock

    # Create a mock box top detector
    mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=True)

    # Create the box detector with dependency injection and a reset offset
    # The reset offset prevents false positives by requiring the box to move further away
    box_detector = BoxDetector(mock_detector, reset_offset=0.1)

    # Example 1: Using context manager (recommended)
    print("=== Using context manager ===")
    try:
        for detected_box in box_detector.detect_boxes():
            if detected_box is not None:
                print("Detected box:")
                pprint(detected_box)
    except KeyboardInterrupt:
        logger.info("Stopping box detection...")
