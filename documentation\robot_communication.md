
# Python Robot Communication
## PickPlace
1. Robot: ReadyForPickPlace = True
2. Python: PickPlaceFrom = (x, y, z, roll, pitch, yaw)
3. Python: PickPlaceTo = (x, y, z, roll, pitch, yaw)
4. Python: PickPlaceStart = True
5. Robot: Unreachable = True / False (True if place position is unreachable, jump to 14)
6.  Robot: ConveyorLock = True
7.  *Robot moves into conveyor space*
8.  *Pick happens*
9.  Robot: NoPick = True / False (True if no vaccum)
10. *Robot moves out of conveyor space*
11. Robot: ConveyorLock = False
12. Robot: LostCargo = True / False (True if vaccum lost during movement)
13. *Place happens*
14. Robot: ReadyForPickPlace = False
15. Python: PickPlaceStart = False
16. Robot: NopPick = False (Reset)
17. Robot: LostCargo = False (Reset)
18. Robot: Unreachable = False (Reset)

The three error signals can be set to high at any time between 4 and 14 and must be held high until after 15.

## Crash
1. Robot: Crash = True
2. Python: Crash = False

In words: 
Crash can be set to True at any time, Python resets to False.

## CheckReachable
1. Python ReachablePose = (x, y, z, roll, pitch, yaw)
2. Python: ReachableTest = True
3. Robot: ReachableResult = True / False (True if reachable)
4. Robot: ReachableTest = False
