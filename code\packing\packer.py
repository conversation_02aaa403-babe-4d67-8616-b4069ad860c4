from __future__ import annotations

from dataclasses import dataclass, field
from typing import Deque, Generator, Iterable, List, Optional, Sequence
from collections import deque

import math
import tomlkit

from .protocols import (
    Box3D,
    Crate,
    PhysicsSimulatorProtocol,
    PlacementHeuristicProtocol,
    PlacedBox,
    Pose,
    ScoringHeuristicProtocol,
    ConstraintProtocol,
)
from .constraints import InsideCrateConstraint, NoOverlapConstraint, VerticalPathClearConstraint


@dataclass
class PackerConfig:
    buffer_size: int
    distance_between: float
    yaw_candidates_deg: Sequence[float]
    neighbors: int
    max_candidates: int
    factor_height: float
    factor_gaps: float
    factor_skewed: float
    factor_volume: float
    max_skew_deg: float


def load_packing_config(path: str = "config/packing.toml") -> PackerConfig:
    with open(path, "r") as f:
        cfg = tomlkit.load(f)
    return PackerConfig(
        buffer_size=cfg["buffer"]["size"],
        distance_between=cfg["placement"]["distance_between"],
        yaw_candidates_deg=list(cfg["placement"]["yaw_candidates_deg"]),
        neighbors=cfg["placement"]["neighbors"],
        max_candidates=cfg["placement"]["max_candidates"],
        factor_height=cfg["scoring"]["factor_height"],
        factor_gaps=cfg["scoring"]["factor_gaps"],
        factor_skewed=cfg["scoring"]["factor_skewed"],
        factor_volume=cfg["scoring"]["factor_volume"],
        max_skew_deg=cfg["scoring"]["max_skew_deg"],
    )


@dataclass
class PackingResult:
    volume_filled_pct: float
    placements: List[PlacedBox]


@dataclass
class Packer:
    crate: Crate
    simulator: PhysicsSimulatorProtocol
    placement_heuristic: PlacementHeuristicProtocol
    scoring_heuristics: Sequence[ScoringHeuristicProtocol]
    constraints: Sequence[ConstraintProtocol] = field(
        default_factory=lambda: [
            InsideCrateConstraint(),
            NoOverlapConstraint(),
            VerticalPathClearConstraint(),
        ]
    )
    yaw_candidates_deg: Sequence[float] = (0.0, 90.0)
    buffer_size: int = 4

    placed: List[PlacedBox] = field(default_factory=list)

    def pack_stream(self, box_stream: Iterable[Box3D], visualizer=None) -> PackingResult:
        # Reset simulator world
        self.simulator.world_reset(self.crate, self.placed)

        buffer: Deque[Box3D] = deque()
        total_volume = 0.0
        max_volume = self.crate.width * self.crate.depth * self.crate.max_height

        for box in box_stream:
            buffer.append(box)
            if len(buffer) < self.buffer_size:
                continue

            # Choose best of buffer
            best_idx = None
            best_score = -1e18
            best_settled: Optional[PlacedBox] = None

            for idx, b in enumerate(list(buffer)[: self.buffer_size]):
                # Candidates positions (x, y) with yaw from config
                proposals = []
                for x, y, _ in self.placement_heuristic.propose(
                    self.crate, b, self.placed, max_candidates=80
                ):
                    for yaw_deg in self.yaw_candidates_deg:
                        proposals.append((x, y, math.radians(yaw_deg)))

                for x, y, yaw in proposals:
                    candidate = PlacedBox(
                        box=b, pose=Pose(x=x, y=y, z=self._surface_z_at(x, y, b, yaw), yaw=yaw)
                    )
                    candidate = self._adjust_vertical_clearance(candidate)
                    # Apply injected constraints
                    violated = False
                    for c in self.constraints:
                        if not c.check(self.crate, candidate, self.placed, self.simulator):
                            violated = True
                            break
                    if violated:
                        continue

                    settled = self.simulator.simulate_drop(self.crate, candidate)
                    if settled is None:
                        continue
                    # Re-validate constraints on settled pose to avoid numeric overlaps
                    violated = False
                    for c in self.constraints:
                        if not c.check(self.crate, settled, self.placed, self.simulator):
                            violated = True
                            break
                    if violated:
                        continue

                    # Aggregate scores from all scoring heuristics
                    score = 0.0
                    for sh in self.scoring_heuristics:
                        score += sh.score(self.crate, self.simulator, settled, self.placed)

                    if score > best_score:
                        best_score = score
                        best_idx = idx
                        best_settled = settled

            # If we found a placement, commit it and remove box from buffer
            if best_settled is not None and best_idx is not None:
                self.simulator.place_static(self.crate, best_settled)
                self.placed.append(best_settled)
                total_volume += (
                    best_settled.box.width * best_settled.box.depth * best_settled.box.height
                )
                # remove the selected buffer element
                buffer_list = list(buffer)
                buffer_list.pop(best_idx)
                buffer = deque(buffer_list)
                if visualizer is not None:
                    visualizer.update(self.crate, self.placed)
            else:
                # Eject the first box if none could be placed
                buffer.popleft()

        # Drain remaining buffer
        while buffer:
            best_idx = None
            best_score = -1e18
            best_settled = None
            for idx, b in enumerate(list(buffer)[: self.buffer_size]):
                proposals = []
                for x, y, _ in self.placement_heuristic.propose(
                    self.crate, b, self.placed, max_candidates=80
                ):
                    for yaw_deg in self.yaw_candidates_deg:
                        proposals.append((x, y, math.radians(yaw_deg)))
                for x, y, yaw in proposals:
                    candidate = PlacedBox(
                        box=b, pose=Pose(x=x, y=y, z=self._surface_z_at(x, y, b, yaw), yaw=yaw)
                    )
                    candidate = self._adjust_vertical_clearance(candidate)
                    violated = False
                    for c in self.constraints:
                        if not c.check(self.crate, candidate, self.placed, self.simulator):
                            violated = True
                            break
                    if violated:
                        continue
                    settled = self.simulator.simulate_drop(self.crate, candidate)
                    if settled is None:
                        continue
                    violated = False
                    for c in self.constraints:
                        if not c.check(self.crate, settled, self.placed, self.simulator):
                            violated = True
                            break
                    if violated:
                        continue
                    score = 0.0
                    for sh in self.scoring_heuristics:
                        score += sh.score(self.crate, self.simulator, settled, self.placed)
                    if score > best_score:
                        best_score = score
                        best_idx = idx
                        best_settled = settled
            if best_settled is not None and best_idx is not None:
                self.simulator.place_static(self.crate, best_settled)
                self.placed.append(best_settled)
                total_volume += (
                    best_settled.box.width * best_settled.box.depth * best_settled.box.height
                )
                buffer_list = list(buffer)
                buffer_list.pop(best_idx)
                buffer = deque(buffer_list)
                if visualizer is not None:
                    visualizer.update(self.crate, self.placed)
            else:
                buffer.popleft()

        volume_pct = 100.0 * total_volume / max_volume if max_volume > 0 else 0.0
        return PackingResult(volume_filled_pct=volume_pct, placements=self.placed)

    def _adjust_vertical_clearance(self, candidate: PlacedBox) -> PlacedBox:
        """Ensure candidate bottom z is at least the top of any XY-overlapping placed box."""
        z = candidate.pose.z
        eff_w = (
            candidate.box.width
            if abs(math.cos(candidate.pose.yaw)) >= abs(math.sin(candidate.pose.yaw))
            else candidate.box.depth
        )
        eff_d = (
            candidate.box.depth
            if abs(math.cos(candidate.pose.yaw)) >= abs(math.sin(candidate.pose.yaw))
            else candidate.box.width
        )
        c_min_x, c_max_x = candidate.pose.x, candidate.pose.x + eff_w
        c_min_y, c_max_y = candidate.pose.y, candidate.pose.y + eff_d
        for pb in self.placed:
            p_eff_w = (
                pb.box.width
                if abs(math.cos(pb.pose.yaw)) >= abs(math.sin(pb.pose.yaw))
                else pb.box.depth
            )
            p_eff_d = (
                pb.box.depth
                if abs(math.cos(pb.pose.yaw)) >= abs(math.sin(pb.pose.yaw))
                else pb.box.width
            )
            p_min_x, p_max_x = pb.pose.x, pb.pose.x + p_eff_w
            p_min_y, p_max_y = pb.pose.y, pb.pose.y + p_eff_d
            overlap_x = (c_min_x < p_max_x) and (p_min_x < c_max_x)
            overlap_y = (c_min_y < p_max_y) and (p_min_y < c_max_y)
            if overlap_x and overlap_y:
                z = max(z, pb.pose.z + pb.box.height)
        if z != candidate.pose.z:
            candidate = PlacedBox(
                candidate.box,
                Pose(x=candidate.pose.x, y=candidate.pose.y, z=z, yaw=candidate.pose.yaw),
            )
        return candidate

    def _surface_z_at(self, x: float, y: float, box: Box3D, yaw: float) -> float:
        """Estimate bottom z at (x,y) given the candidate footprint orientation.
        We take the top of any placed box that overlaps the candidate's footprint in XY.
        """
        eff_w = box.width if abs(math.cos(yaw)) >= abs(math.sin(yaw)) else box.depth
        eff_d = box.depth if abs(math.cos(yaw)) >= abs(math.sin(yaw)) else box.width
        c_min_x, c_max_x = x, x + eff_w
        c_min_y, c_max_y = y, y + eff_d
        z = 0.0
        for pb in self.placed:
            p_eff_w = (
                pb.box.width
                if abs(math.cos(pb.pose.yaw)) >= abs(math.sin(pb.pose.yaw))
                else pb.box.depth
            )
            p_eff_d = (
                pb.box.depth
                if abs(math.cos(pb.pose.yaw)) >= abs(math.sin(pb.pose.yaw))
                else pb.box.width
            )
            p_min_x, p_max_x = pb.pose.x, pb.pose.x + p_eff_w
            p_min_y, p_max_y = pb.pose.y, pb.pose.y + p_eff_d
            overlap_x = (c_min_x < p_max_x) and (p_min_x < c_max_x)
            overlap_y = (c_min_y < p_max_y) and (p_min_y < c_max_y)
            if overlap_x and overlap_y:
                z = max(z, pb.pose.z + pb.box.height)
        return z
