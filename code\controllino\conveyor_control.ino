/*
 * Conveyor Control System for Bleckmann Box Stacking
 *
 * This Arduino script controls a 4-part conveyor system where:
 * - Each part has a sensor at the end to detect boxes
 * - Boxes move to the next part only if the next part is free
 * - Both current and next parts must be moving for transfer
 *
 * See README.md for detailed setup and usage instructions
 */

#include "config.h"

// Pin definitions using config.h
const int SENSOR_PINS[NUM_PARTS] = {SENSOR_PIN_1, SENSOR_PIN_2, SENSOR_PIN_3, SENSOR_PIN_4};
const int MOTOR_PINS[NUM_PARTS] = {MOTOR_PIN_1, MOTOR_PIN_2, MOTOR_PIN_3, MOTOR_PIN_4};
const int LED_PINS[NUM_PARTS] = {LED_PIN_1, LED_PIN_2, LED_PIN_3, LED_PIN_4};

// System states
bool sensorStates[NUM_PARTS] = {false, false, false, false}; // Current sensor readings
bool motorStates[NUM_PARTS] = {false, false, false, false};  // Current motor states
bool boxPresent[NUM_PARTS] = {false, false, false, false};   // Box presence on each part

// Timing variables
unsigned long lastSensorRead = 0;
unsigned long lastStatusReport = 0;

// Debouncing variables
bool lastSensorStates[NUM_PARTS] = {false, false, false, false};
unsigned long sensorDebounceTime[NUM_PARTS] = {0, 0, 0, 0};

// Safety variables
unsigned long motorStartTime[NUM_PARTS] = {0, 0, 0, 0};
bool emergencyStopActive = false;

// Eject control variables
bool ejectRequested = false;
bool ejectInProgress = false;
bool lastPartHadBox = false;

// Lock control variables
bool lockAcquired = false;
bool releaseRequested = false;
bool releasingLock = false;

void setup()
{
  Serial.begin(9600);
  Serial.println("Conveyor Control System Starting...");

  // Initialize sensor pins as inputs with pullup resistors
  for (int i = 0; i < NUM_PARTS; i++)
  {
    pinMode(SENSOR_PINS[i], INPUT_PULLUP);
    pinMode(MOTOR_PINS[i], OUTPUT);
    pinMode(LED_PINS[i], OUTPUT);

    // Initialize all motors as stopped
    digitalWrite(MOTOR_PINS[i], LOW);
    digitalWrite(LED_PINS[i], LOW);
  }

// Initialize emergency stop pin if defined
#ifdef EMERGENCY_STOP_PIN
  pinMode(EMERGENCY_STOP_PIN, INPUT_PULLUP);
#endif

  Serial.println("System initialized. Ready for operation.");
}

void loop()
{
  // Check emergency stop
  checkEmergencyStop();

  // Handle serial commands
  handleSerialCommands();

  // Read sensors at regular intervals
  if (millis() - lastSensorRead >= SENSOR_READ_INTERVAL)
  {
    readSensors();
    updateBoxPresence();
    handleEjectLogic();
    handleLockRelease();
    controlConveyors();
    updateLEDs();
    lastSensorRead = millis();
  }

// Print status report periodically
#if DEBUG_MODE
  if (millis() - lastStatusReport >= STATUS_REPORT_INTERVAL)
  {
    printSystemStatus();
    lastStatusReport = millis();
  }
#endif
}

void readSensors()
{
  for (int i = 0; i < NUM_PARTS; i++)
  {
    bool currentReading = SENSOR_ACTIVE_LOW ? !digitalRead(SENSOR_PINS[i]) : digitalRead(SENSOR_PINS[i]);

    // Debounce the sensor reading
    if (currentReading != lastSensorStates[i])
    {
      sensorDebounceTime[i] = millis();
    }

    if ((millis() - sensorDebounceTime[i]) > DEBOUNCE_DELAY)
    {
      if (currentReading != sensorStates[i])
      {
        sensorStates[i] = currentReading;

// Log sensor state changes
#if DEBUG_MODE
        Serial.print("Sensor ");
        Serial.print(i + 1);
        Serial.print(": ");
        Serial.println(sensorStates[i] ? "BLOCKED" : "FREE");
#endif
      }
    }

    lastSensorStates[i] = currentReading;
  }
}

void updateBoxPresence()
{
  for (int i = 0; i < NUM_PARTS; i++)
  {
    boxPresent[i] = sensorStates[i];
  }
}

void controlConveyors()
{
  if (emergencyStopActive)
  {
    // Stop all motors in emergency
    for (int i = 0; i < NUM_PARTS; i++)
    {
      setMotorState(i, false);
    }
    return;
  }

  // Check if lock is acquired - no movement without lock
  if (!lockAcquired)
  {
    for (int i = 0; i < NUM_PARTS; i++)
    {
      setMotorState(i, false);
    }
    return;
  }

  // If release is requested, stop all motors
  if (releaseRequested || releasingLock)
  {
    for (int i = 0; i < NUM_PARTS; i++)
    {
      setMotorState(i, false);
    }
    return;
  }

  // Part 1: Always run if there's a box or if part 2 is free and running
  bool shouldRunPart1 = boxPresent[0] || (!boxPresent[1] && shouldRunPart(1));
  setMotorState(0, shouldRunPart1);

  // Parts 2-4: Run based on box transfer logic
  for (int i = 1; i < NUM_PARTS; i++)
  {
    bool shouldRun = shouldRunPart(i);
    setMotorState(i, shouldRun);
  }
}

bool shouldRunPart(int partIndex)
{
  // Part should run if:
  // 1. There's a box on this part, OR
  // 2. Previous part has a box and this part is free (to receive the box)

  bool hasBox = boxPresent[partIndex];
  bool canReceiveBox = false;

  if (partIndex > 0)
  {
    bool prevHasBox = boxPresent[partIndex - 1];
    bool thisPartFree = !boxPresent[partIndex];
    canReceiveBox = prevHasBox && thisPartFree;
  }

  // Special case for last part (part 4): only run during eject or if eject is in progress
  if (partIndex == 3)
  {
    if (ejectInProgress && hasBox)
    {
      return true; // Keep running until box is ejected
    }
    else if (ejectRequested && hasBox)
    {
      ejectInProgress = true;
      ejectRequested = false;
      return true; // Start ejecting
    }
    else if (!ejectInProgress)
    {
      // Don't let new boxes through unless eject is requested
      return false;
    }
    return false;
  }

  // For other parts: run if has box and next part can receive, or can receive from previous
  if (hasBox && partIndex < 3)
  {
    bool nextPartFree = !boxPresent[partIndex + 1];
    // For part 3, only allow transfer if part 4 is ready for eject
    if (partIndex == 2)
    {
      return nextPartFree && !ejectInProgress;
    }
    return nextPartFree || canReceiveBox;
  }

  return canReceiveBox;
}

void setMotorState(int motorIndex, bool state)
{
  if (motorStates[motorIndex] != state)
  {
    motorStates[motorIndex] = state;
    digitalWrite(MOTOR_PINS[motorIndex], state ? HIGH : LOW);

    Serial.print("Motor ");
    Serial.print(motorIndex + 1);
    Serial.print(": ");
    Serial.println(state ? "RUNNING" : "STOPPED");
  }
}

void updateLEDs()
{
  for (int i = 0; i < NUM_PARTS; i++)
  {
    // LED on if motor is running
    digitalWrite(LED_PINS[i], motorStates[i] ? HIGH : LOW);
  }
}

// Check emergency stop button
void checkEmergencyStop()
{
#ifdef EMERGENCY_STOP_PIN
  bool emergencyPressed = !digitalRead(EMERGENCY_STOP_PIN); // Active low
  if (emergencyPressed && !emergencyStopActive)
  {
    emergencyStopActive = true;
    Serial.println("EMERGENCY STOP ACTIVATED!");
    emergencyStop();
  }
  else if (!emergencyPressed && emergencyStopActive)
  {
    emergencyStopActive = false;
    Serial.println("Emergency stop released. System ready.");
  }
#endif
}

// Debug function to print system status
void printSystemStatus()
{
  Serial.println("=== System Status ===");
  for (int i = 0; i < NUM_PARTS; i++)
  {
    Serial.print("Part ");
    Serial.print(i + 1);
    Serial.print(": Sensor=");
    Serial.print(sensorStates[i] ? "BLOCKED" : "FREE");
    Serial.print(", Motor=");
    Serial.print(motorStates[i] ? "RUNNING" : "STOPPED");
    Serial.print(", Box=");
    Serial.println(boxPresent[i] ? "PRESENT" : "ABSENT");
  }
  Serial.println("====================");
}

// Function to handle emergency stop (can be called via serial command)
void emergencyStop()
{
  Serial.println("EMERGENCY STOP ACTIVATED!");
  for (int i = 0; i < NUM_PARTS; i++)
  {
    digitalWrite(MOTOR_PINS[i], LOW);
    motorStates[i] = false;
    digitalWrite(LED_PINS[i], LOW);
  }
}

// Handle eject logic for part 4
void handleEjectLogic()
{
  // Track if part 4 had a box in the previous cycle
  bool currentBoxOnPart4 = boxPresent[3];

  // If eject was in progress and box is no longer present, eject is complete
  if (ejectInProgress && lastPartHadBox && !currentBoxOnPart4)
  {
    ejectInProgress = false;
    Serial.println("EJECT_SUCCESS");
#if DEBUG_MODE
    Serial.println("Box successfully ejected from Part 4");
#endif
  }

  // Update the last state
  lastPartHadBox = currentBoxOnPart4;
}

// Handle lock release logic
void handleLockRelease()
{
  if (releaseRequested && !releasingLock)
  {
    // Check if any motors are currently running
    bool anyMotorRunning = false;
    for (int i = 0; i < NUM_PARTS; i++)
    {
      if (motorStates[i])
      {
        anyMotorRunning = true;
        break;
      }
    }

    if (anyMotorRunning)
    {
      releasingLock = true;
      Serial.println("RELEASING_LOCK");
#if DEBUG_MODE
      Serial.println("Stopping all motors before releasing lock");
#endif
    }
    else
    {
      // No motors running, can release immediately
      lockAcquired = false;
      releaseRequested = false;
      Serial.println("RELEASED_LOCK");
#if DEBUG_MODE
      Serial.println("Lock released immediately - no motors were running");
#endif
    }
  }
  else if (releasingLock)
  {
    // Check if all motors have stopped
    bool anyMotorRunning = false;
    for (int i = 0; i < NUM_PARTS; i++)
    {
      if (motorStates[i])
      {
        anyMotorRunning = true;
        break;
      }
    }

    if (!anyMotorRunning)
    {
      // All motors stopped, can now release lock
      lockAcquired = false;
      releaseRequested = false;
      releasingLock = false;
      Serial.println("RELEASED_LOCK");
#if DEBUG_MODE
      Serial.println("All motors stopped - lock released");
#endif
    }
  }
}

// Handle serial commands
void handleSerialCommands()
{
  if (Serial.available())
  {
    String command = Serial.readString();
    command.trim();

    if (command == "ACQUIRE")
    {
      if (!lockAcquired)
      {
        lockAcquired = true;
        releaseRequested = false;
        releasingLock = false;
        Serial.println("LOCK_ACQUIRED");
#if DEBUG_MODE
        Serial.println("Conveyor lock acquired - movement enabled");
#endif
      }
      else
      {
        Serial.println("LOCK_ALREADY_ACQUIRED");
      }
    }
    else if (command == "RELEASE")
    {
      if (lockAcquired)
      {
        releaseRequested = true;
#if DEBUG_MODE
        Serial.println("Lock release requested");
#endif
      }
      else
      {
        Serial.println("LOCK_NOT_ACQUIRED");
      }
    }
    else if (command == "EJECT")
    {
      if (!lockAcquired)
      {
        Serial.println("EJECT_LOCK_NOT_ACQUIRED");
        return;
      }

      if (boxPresent[3]) // Check if there's a box on part 4
      {
        if (!ejectInProgress)
        {
          ejectRequested = true;
#if DEBUG_MODE
          Serial.println("Eject command received - starting eject sequence");
#endif
        }
        else
        {
          Serial.println("EJECT_IN_PROGRESS");
        }
      }
      else
      {
        Serial.println("EJECT_NO_BOX_PRESENT");
      }
    }
    else if (command == "STOP")
    {
      emergencyStop();
    }
    else if (command == "STATUS")
    {
      printSystemStatus();
    }
    else if (command == "RESET")
    {
      Serial.println("System reset...");
      lockAcquired = false;
      releaseRequested = false;
      releasingLock = false;
      ejectRequested = false;
      ejectInProgress = false;
      lastPartHadBox = false;
      setup();
    }
  }
}
