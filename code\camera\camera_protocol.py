from typing import Literal, Optional, Protocol, Self

import open3d as o3d


class CameraProtocol(Protocol):
    """Protocol defining the interface for camera implementations."""

    def _start_capture(self) -> bool:
        """
        Start the camera capture.

        Returns:
            bool: True if capture started successfully, False otherwise
        """
        ...

    def _stop_capture(self) -> None:
        """Stop the camera capture."""
        ...

    def get_pointcloud(self) -> Optional[o3d.geometry.PointCloud]:
        """
        Get a point cloud from the camera.

        Returns:
            Open3D point cloud or None if failed
        """
        ...

    def get_device_info(self) -> dict:
        """
        Get information about the connected camera device.

        Returns:
            Dictionary containing device information
        """
        ...

    def __enter__(self) -> Self:
        """Enter the context manager."""
        ...

    def __exit__(self, exc_type, exc_val, exc_tb) -> Literal[False]:
        """Exit the context manager."""
        ...
