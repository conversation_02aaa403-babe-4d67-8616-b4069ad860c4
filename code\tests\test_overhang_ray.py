from packing.protocols import Box3<PERSON>, <PERSON>rate, Pose, PlacedBox
from packing.scores.overhang_score import OverhangScore
from packing.simulator_bullet import BulletPhysicsSimulator


def test_overhang_ray_increases_with_gap():
    crate = Crate(0.6, 0.6, 0.6)
    sim = BulletPhysicsSimulator()
    # One support box on the ground
    base = PlacedBox(Box3D(0.3, 0.3, 0.1), Pose(0.0, 0.0, 0.0, 0.0))
    sim.world_reset(crate, [base])
    # Candidate partially overhangs base
    cand = PlacedBox(Box3D(0.3, 0.3, 0.1), Pose(0.15, 0.0, 0.1, 0.0))
    # Simulate to set metrics and settled pose
    settled = sim.simulate_drop(crate, cand)
    assert settled is not None

    s = OverhangScore(weight=1.0, grid_resolution=0.05)
    val = s.score(crate, sim, settled, [base])
    # Should be negative (penalty); larger overhang area -> more negative
    assert val < 0.0

