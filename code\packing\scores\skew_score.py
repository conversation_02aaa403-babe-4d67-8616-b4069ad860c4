import math
from dataclasses import dataclass
from typing import Iterable

from ..protocols import Crate, PhysicsSimulatorProtocol, PlacedBox, ScoringHeuristicProtocol


@dataclass
class SkewScore(ScoringHeuristicProtocol):
    weight: float = 1.0
    max_skew_deg: float = 10.0

    def score(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
    ) -> float:
        metrics = getattr(simulator, "candidate_metrics", lambda: None)()
        if metrics is None:
            return -1e9
        euler = metrics["euler"]
        max_skew = math.radians(self.max_skew_deg)

        def skew_term(angle):
            delta = abs(abs(angle) - round(abs(angle) / (math.pi / 2)) * (math.pi / 2))
            return (1.0 / (delta + 1.0)) ** 3

        if any(abs(a) > max_skew for a in euler):
            return -1e6
        return self.weight * (skew_term(euler[0]) + skew_term(euler[1]) + skew_term(euler[2]))
