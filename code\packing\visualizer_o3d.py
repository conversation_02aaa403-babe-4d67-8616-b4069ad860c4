from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable, List

import time
import numpy as np
import open3d as o3d

from .protocols import PackerVisualizerProtocol, Crate, PlacedBox


@dataclass
class Open3DVisualizer(PackerVisualizerProtocol):
    show_window: bool = True
    update_delay_s: float = 0.3  # wait after each update so it’s visible

    def __post_init__(self):
        self._geometries: list[o3d.geometry.Geometry] = []
        self._vis: o3d.visualization.Visualizer | None = None
        if self.show_window:
            self._vis = o3d.visualization.Visualizer()
            self._vis.create_window(window_name="Packer", visible=True)

    def _make_box(self, x, y, z, w, d, h, color=(0.8, 0.4, 0.2)):
        mesh = o3d.geometry.TriangleMesh.create_box(width=w, height=h, depth=d)
        mesh.compute_vertex_normals()
        mesh.paint_uniform_color(color)
        mesh.translate((x, z, y))  # z-up, swap y/z for visualization
        return mesh

    def _apply_camera(self, vis: o3d.visualization.Visualizer, crate: Crate):
        ctr = vis.get_view_control()
        # Look at crate center, from above at an angle
        lookat = np.array([crate.width / 2.0, crate.max_height / 2.0, crate.depth / 2.0])
        front = np.array([-0.5, 0.8, -0.5])  # direction towards scene
        up = np.array([0.0, 1.0, 0.0])  # +Y is up in our viz mapping
        ctr.set_lookat(lookat)
        ctr.set_front(front / np.linalg.norm(front))
        ctr.set_up(up)
        ctr.set_zoom(0.7)

    def update(self, crate: Crate, placed: Iterable[PlacedBox]) -> None:
        if not self.show_window or self._vis is None:
            return
        # Clear and re-draw
        self._vis.clear_geometries()
        self._geometries.clear()
        # Draw crate base
        plane = self._make_box(0, 0, 0, crate.width, crate.depth, 0.001, color=(0.6, 0.6, 0.6))
        self._geometries.append(plane)
        self._vis.add_geometry(plane)
        # Draw boxes
        for pb in placed:
            box = self._make_box(
                pb.pose.x, pb.pose.y, pb.pose.z, pb.box.width, pb.box.depth, pb.box.height
            )
            self._geometries.append(box)
            self._vis.add_geometry(box)
        self._apply_camera(self._vis, crate)
        self._vis.poll_events()
        self._vis.update_renderer()
        if self.update_delay_s > 0:
            time.sleep(self.update_delay_s)

    def block_until_closed(self, crate: Crate, placed: Iterable[PlacedBox]) -> None:
        if not self.show_window or self._vis is None:
            return
        self._apply_camera(self._vis, crate)
        self._vis.run()  # blocks until closed by user
