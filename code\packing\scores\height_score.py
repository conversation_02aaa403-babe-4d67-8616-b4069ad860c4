from dataclasses import dataclass
from typing import Iterable

from ..protocols import Crate, PhysicsSimulatorProtocol, PlacedBox, ScoringHeuristicProtocol


@dataclass
class HeightScore(ScoringHeuristicProtocol):
    weight: float = 1.0

    def score(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
    ) -> float:
        highest_point = candidate.pose.z + candidate.box.height
        if highest_point > crate.max_height + 1e-6:
            return -1e9
        return self.weight * (crate.max_height - highest_point)
