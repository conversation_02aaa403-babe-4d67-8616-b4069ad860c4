# ========================== #
# Configuration part by user #
# ========================== #

[boxes]
min_box_dimension = 0.09 # 9 cm
max_box_height = 0.45    # 45 cm

[height]
deadzone_above_conveyor = 0.09 # 9 cm
marker_thickness = 0.002       # 2 mm

[markers]
upstream_marker_id = 7
width_marker_id = 3
downstream_marker_id = 6


# ======================================================================================== #
# Values below are calculated automatically by running calibration/conveyor_calibration.py #
# ======================================================================================== #

[calibration]
transform = [[0.0026651163181065723, 0.9997728757329232, 0.02114459987164851, 0.5499519717352552], [0.9996739009815894, -0.0032006664393158307, 0.025334708022705, 0.3086790047068152], [0.025396630706895684, 0.021070184694619905, -0.9994553809278693, 1.22628651894244], [0.0, 0.0, 0.0, 1.0]]
roi = [1.0275947310848894, 0.5693960003361047]
