from dataclasses import dataclass, field
from typing import Generator, Iterable, Optional, Protocol, Tuple

import math

from scipy.spatial.transform import Rotation as R


# ---- Domain protocols ----


class PlacementHeuristicProtocol(Protocol):
    """Proposes candidate drop poses for a given box and crate state.

    Implementations should be deterministic for the same inputs to make comparisons reproducible.
    """

    def propose(
        self, crate: "Crate", box: "Box3D", placed: Iterable["PlacedBox"], max_candidates: int
    ) -> Iterable["PlacedBox"]: ...


class ScoringHeuristicProtocol(Protocol):
    """Scores a placed box candidate. Higher is better."""

    def score(
        self,
        crate: "Crate",
        simulator: "PhysicsSimulatorProtocol",
        candidate: "PlacedBox",
        placed: Iterable["PlacedBox"],
    ) -> float: ...


class PhysicsSimulatorProtocol(Protocol):
    """Abstract physics simulator used to evaluate and finalize placements."""

    def simulate_drop(
        self, crate: "Crate", candidate: "PlacedBox", max_steps: int = 150
    ) -> Optional["PlacedBox"]:
        """Simulate a vertical drop from above. Returns final settled pose or None if invalid."""
        ...

    def vertical_clearance_ok(self, crate: "Crate", candidate: "PlacedBox") -> bool:
        """Check that the box can travel vertically down to its final Z without collisions.
        Implement a conservative, fast check (e.g., AABB column test)."""
        ...

    def place_static(self, crate: "Crate", placed: "PlacedBox") -> None:
        """Commit a settled box as static to the world (frozen)."""
        ...

    def world_reset(self, crate: "Crate", placed: Iterable["PlacedBox"]) -> None:
        """Rebuild the static world from scratch for determinism (optional)."""
        ...


class PackerInputProtocol(Protocol):
    """Provides a stream of incoming boxes for packing."""

    def stream(self) -> Generator["Box3D", None, None]: ...


class PackerVisualizerProtocol(Protocol):
    """Optional visual feedback for packing."""

    def update(self, crate: "Crate", placed: Iterable["PlacedBox"]) -> None: ...


class ConstraintProtocol(Protocol):
    """Constraint that must hold for a candidate placement.

    Implementations can check bounds, vertical path collisions, etc.
    Return True if the candidate is acceptable, False otherwise.
    """

    def check(
        self,
        crate: "Crate",
        candidate: "PlacedBox",
        placed: Iterable["PlacedBox"],
        simulator: Optional["PhysicsSimulatorProtocol"] = None,
    ) -> bool: ...


# ---- Core dataclasses kept minimal to avoid import cycles ----


@dataclass
class Box3D:
    width: float
    depth: float
    height: float
    id: Optional[int] = None


@dataclass
class Pose:
    x: float
    y: float
    z: float
    r: field()  # Rotation matrix


@dataclass
class PlacedBox:
    box: Box3D
    pose: Pose


@dataclass
class Crate:
    width: float
    depth: float
    max_height: float
