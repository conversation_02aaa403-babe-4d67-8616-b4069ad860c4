from packing.streams import FixedListStream, RandomUniformStream, CyclicSetStream
from packing.protocols import Box3D


def test_fixed_list_stream():
    xs = [Box3D(0.1,0.2,0.3), Box3D(0.2,0.2,0.2)]
    s = FixedListStream(xs)
    assert list(s.stream()) == xs


def test_random_uniform_stream_seed():
    s1 = RandomUniformStream(count=3, w_range=(0.1,0.2), d_range=(0.1,0.2), h_range=(0.1,0.2), seed=1)
    s2 = RandomUniformStream(count=3, w_range=(0.1,0.2), d_range=(0.1,0.2), h_range=(0.1,0.2), seed=1)
    a = list(s1.stream())
    b = list(s2.stream())
    assert a[0].width == b[0].width and a[0].depth == b[0].depth and a[0].height == b[0].height


def test_cyclic_set_stream():
    base = [Box3D(0.1,0.1,0.1), Box3D(0.2,0.2,0.2)]
    s = CyclicSetStream(set_boxes=base, repeat=2)
    out = list(s.stream())
    assert out == base + base

