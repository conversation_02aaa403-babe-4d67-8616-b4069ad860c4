from __future__ import annotations

import random
from dataclasses import dataclass
from typing import Generator, Iterable, Optional

from .protocols import Box3D, PackerInputProtocol


@dataclass
class FixedListStream(PackerInputProtocol):
    boxes: Iterable[Box3D]
    def stream(self) -> Generator[Box3D, None, None]:
        for b in self.boxes:
            yield b


@dataclass
class RandomUniformStream(PackerInputProtocol):
    count: int
    w_range: tuple[float, float]
    d_range: tuple[float, float]
    h_range: tuple[float, float]
    seed: Optional[int] = None

    def stream(self) -> Generator[Box3D, None, None]:
        rng = random.Random(self.seed)
        for _ in range(self.count):
            w = rng.uniform(*self.w_range)
            d = rng.uniform(*self.d_range)
            h = rng.uniform(*self.h_range)
            yield Box3D(w, d, h)


@dataclass
class CyclicSetStream(PackerInputProtocol):
    set_boxes: list[Box3D]
    repeat: int

    def stream(self) -> Generator[Box3D, None, None]:
        for _ in range(self.repeat):
            for b in self.set_boxes:
                yield b

