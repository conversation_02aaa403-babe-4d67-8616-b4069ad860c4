import numpy as np


def get_transform(origin, x_direction, y_direction):
    """
    Gets the transformation matrix from the origin and the two directions.

    Args:
        origin: The origin of the coordinate system.
        x_direction: The x direction of the coordinate system.
        y_direction: The y direction of the coordinate system. Note that this will be orthogonalized to the x direction.

    Returns:
        The transformation matrix.

    Raises:
        ValueError: If input vectors are zero, parallel, or would result in invalid transformation.
    """
    # Convert to numpy arrays and ensure they are float64
    origin = np.asarray(origin, dtype=np.float64)
    x_direction = np.asarray(x_direction, dtype=np.float64)
    y_direction = np.asarray(y_direction, dtype=np.float64)

    # Check for zero vectors
    x_norm = np.linalg.norm(x_direction)
    y_norm = np.linalg.norm(y_direction)

    if x_norm == 0.0:
        raise ValueError("x_direction cannot be a zero vector")
    if y_norm == 0.0:
        raise ValueError("y_direction cannot be a zero vector")

    # Normalize x_direction first
    x_direction = x_direction / x_norm

    # Orthogonalize y_direction to x_direction using Gram-Schmidt
    y_direction = y_direction - np.dot(y_direction, x_direction) * x_direction

    # Check if y_direction became zero after orthogonalization (parallel vectors)
    y_norm_after_ortho = np.linalg.norm(y_direction)
    if y_norm_after_ortho == 0.0:
        raise ValueError("x_direction and y_direction cannot be parallel")

    # Normalize the orthogonalized y_direction
    y_direction = y_direction / y_norm_after_ortho

    # Compute z_direction as cross product
    z_direction = np.cross(x_direction, y_direction)

    # Build the transformation matrix
    transform = np.eye(4)
    transform[:3, 0] = x_direction
    transform[:3, 1] = y_direction
    transform[:3, 2] = z_direction
    transform[:3, 3] = origin
    return transform


def invert_transform(transform):
    """Inverts the transformation matrix."""
    if not np.allclose(np.linalg.det(transform[:3, :3]), 1.0, atol=1e-10):
        raise ValueError("Transformation matrix must be a pure rotation matrix")
    if not np.allclose(transform[3, 3], 1.0, atol=1e-10):
        raise ValueError("Transformation matrix must be a homogeneous matrix")
    if not np.allclose(transform[3, :3], 0.0, atol=1e-10):
        raise ValueError("Transformation matrix must be a homogeneous matrix")
    # Fast transform
    # T = | R  |  p |
    #     | 0  |  1 |
    # T⁻¹ = | Rᵀ |  -Rᵀp |
    #       | 0  |     1 |

    R = transform[:3, :3]
    p = transform[:3, 3]

    # Create the inverse transformation matrix
    R_T = R.T
    neg_R_T_p = (-R.T @ p).reshape(-1, 1)
    bottom_row = np.array([[0.0, 0.0, 0.0, 1.0]])

    return np.block([[R_T, neg_R_T_p], [bottom_row]])
