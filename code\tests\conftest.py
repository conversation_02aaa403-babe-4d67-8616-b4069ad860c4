import pytest

from packing.protocols import Crate
from packing.simulator_bullet import BulletPhysicsSimulator


@pytest.fixture
def crate():
    # A small crate used across packing/overhang tests
    return Crate(0.6, 0.6, 0.6)


@pytest.fixture
def sim():
    # Fresh simulator for each test; world_reset will be called per test
    return BulletPhysicsSimulator()


def pytest_collection_modifyitems(config, items):
    """Automatically skip tests marked 'realsense' if pyrealsense2 is not installed."""
    try:
        import pyrealsense2  # noqa: F401

        has_pyrealsense2 = True
    except Exception:
        has_pyrealsense2 = False

    if not has_pyrealsense2:
        skip = pytest.mark.skip(reason="pyrealsense2 not installed")
        for item in items:
            if "realsense" in item.keywords:
                item.add_marker(skip)
