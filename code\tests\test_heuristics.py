import math
import pytest

from packing.protocols import Box3<PERSON>, <PERSON><PERSON>, Po<PERSON>, PlacedBox
from packing.heuristics import NeighborCornersHeuristic, HeightScore, GapScore, SkewScore, VolumeBiasScore
from packing.simulator_bullet import BulletPhysicsSimulator


def test_neighbor_corners_yields_corners():
    crate = Crate(1.0, 1.0, 1.0)
    box = Box3D(0.2, 0.2, 0.2)
    heur = NeighborCornersHeuristic(neighbors=0)
    cands = list(heur.propose(crate, box, [], max_candidates=10))
    assert (0.0, 0.0, 0.0) in cands
    assert (crate.width - box.width, 0.0, 0.0) in cands


def test_height_score_lower_is_better():
    crate = Crate(1.0, 1.0, 1.0)
    sim = BulletPhysicsSimulator()
    b = Box3D(0.2, 0.2, 0.2)
    low = PlacedBox(b, Pose(0.0, 0.0, 0.0, 0.0))
    high = PlacedBox(b, Pose(0.0, 0.0, 0.5, 0.0))
    hs = HeightScore(weight=1.0)
    assert hs.score(crate, sim, low, []) > hs.score(crate, sim, high, [])


def test_skew_score_penalizes_large_skew():
    crate = Crate(1.0, 1.0, 1.0)
    sim = BulletPhysicsSimulator()
    b = Box3D(0.2, 0.2, 0.2)
    cand = PlacedBox(b, Pose(0.0, 0.0, 0.0, 0.0))
    ss = SkewScore(weight=1.0, max_skew_deg=1.0)
    # Without candidate_metrics set, returns -inf-ish; acceptable for interface test
    assert ss.score(crate, sim, cand, []) < 0.0


def test_volume_bias_prefers_larger():
    crate = Crate(1.0, 1.0, 1.0)
    sim = BulletPhysicsSimulator()
    small = PlacedBox(Box3D(0.1, 0.1, 0.1), Pose(0.0, 0.0, 0.0, 0.0))
    large = PlacedBox(Box3D(0.3, 0.3, 0.3), Pose(0.0, 0.0, 0.0, 0.0))
    vb = VolumeBiasScore(weight=1.0)
    assert vb.score(crate, sim, large, []) > vb.score(crate, sim, small, [])

