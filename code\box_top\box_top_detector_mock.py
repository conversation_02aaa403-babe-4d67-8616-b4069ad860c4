from typing import Generator, Optional

import numpy as np
from loguru import logger

from box_top import <PERSON>Top
from box_top.box_top_detector_protocol import BoxTopDetectorProtocol


class BoxTopDetectorMock(BoxTopDetectorProtocol):
    """
    Mock box top detector that reads BoxTop data from a text file and streams it.
    """

    def __init__(self, recording_file: str, loop: bool = True):
        """
        Initialize the mock box top detector.

        Args:
            recording_file: Path to the recording file containing BoxTop data
            loop: Whether to loop the recording when it reaches the end
        """
        self.recording_file = recording_file
        self.loop = loop
        self._positions = []
        self._load_box_tops()

    def _load_box_tops(self):
        """Load BoxTop objects from the recording file."""
        try:
            with open(self.recording_file, "r") as f:
                content = f.read()

            # Parse the BoxTop objects from the file
            # The file contains multi-line string representations of BoxTop objects
            lines = content.strip().split("\n")

            current_box_lines = []

            for line in lines:
                line = line.strip()
                if line.startswith("BoxTop("):
                    # Start of a new BoxTop
                    if current_box_lines:
                        # Process the previous BoxTop
                        box_str = " ".join(current_box_lines)
                        box_pos = self._parse_box_tops(box_str)
                        if box_pos:
                            self._positions.append(box_pos)
                    current_box_lines = [line]
                elif line and current_box_lines:
                    # Continue building the current BoxTop
                    current_box_lines.append(line)
                elif line.endswith(")") and current_box_lines:
                    # End of current BoxTop
                    current_box_lines.append(line)
                    box_str = " ".join(current_box_lines)
                    box_pos = self._parse_box_tops(box_str)
                    if box_pos:
                        self._positions.append(box_pos)
                    current_box_lines = []

            # Handle the last BoxTop if any
            if current_box_lines:
                box_str = " ".join(current_box_lines)
                box_pos = self._parse_box_tops(box_str)
                if box_pos:
                    self._positions.append(box_pos)

            logger.info(
                f"Loaded {len(self._positions)} box top positions from {self.recording_file}"
            )

        except FileNotFoundError:
            logger.error(f"Recording file not found: {self.recording_file}")
            self._positions = []
        except Exception as e:
            logger.error(f"Error loading recording file: {e}")
            self._positions = []

    def _parse_box_tops(self, line: str) -> Optional[BoxTop]:
        """
        Parse a BoxTop object from its string representation.

        Args:
            line: String representation of BoxTop

        Returns:
            BoxTop object or None if parsing fails
        """
        try:
            # Remove 'BoxTop(' and the closing ')'
            content = line.strip()
            if not content.startswith("BoxTop(") or not content.endswith(")"):
                return None

            content = content[7:-1]  # Remove 'BoxTop(' and ')'

            # Parse the fields
            center = None
            z_position = None
            extent = None
            rotation = None
            volume = None

            # Split by comma and parse each field
            parts = content.split(",")
            current_field = None
            current_value = ""

            for part in parts:
                part = part.strip()

                if part.startswith("center="):
                    current_field = "center"
                    current_value = part[7:]  # Remove 'center='
                elif part.startswith("z_position="):
                    if current_field == "center":
                        center = self._parse_array(current_value)
                    current_field = "z_position"
                    z_position = float(part[11:])  # Remove 'z_position='
                elif part.startswith("extent="):
                    current_field = "extent"
                    current_value = part[7:]  # Remove 'extent='
                elif part.startswith("rotation="):
                    if current_field == "extent":
                        extent = self._parse_array(current_value)
                    current_field = "rotation"
                    current_value = part[9:]  # Remove 'rotation='
                elif part.startswith("volume="):
                    if current_field == "rotation":
                        rotation = self._parse_array(current_value)
                    current_field = "volume"
                    volume = float(part[7:])  # Remove 'volume='
                else:
                    # Continue building the current value
                    if current_value:
                        current_value += "," + part
                    else:
                        current_value = part

            # Handle the last field if it's an array
            if current_field == "rotation" and rotation is None:
                rotation = self._parse_array(current_value)

            if (
                center is not None
                and z_position is not None
                and extent is not None
                and rotation is not None
                and volume is not None
            ):
                return BoxTop(
                    center=center,
                    z_position=z_position,
                    extent=extent,
                    rotation=rotation,
                    volume=volume,
                )

        except Exception as e:
            logger.debug(f"Error parsing BoxTop: {e}")

        return None

    def _parse_array(self, array_str: str) -> Optional[np.ndarray]:
        """
        Parse a numpy array from its string representation.

        Args:
            array_str: String representation of numpy array

        Returns:
            numpy array or None if parsing fails
        """
        try:
            # Remove 'array(' and ')'
            if array_str.startswith("array("):
                array_str = array_str[6:-1]

            # Remove brackets and split by whitespace
            array_str = array_str.strip("[]")
            values = []

            # Split by comma or whitespace
            parts = array_str.replace(",", " ").split()
            for part in parts:
                part = part.strip()
                if part:
                    values.append(float(part))

            return np.array(values)

        except Exception as e:
            logger.debug(f"Error parsing array: {e}")
            return None

    def detect_box_tops(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Detect box top and yield BoxTop objects as a stream.

        Yields:
            BoxTop objects or None if detection fails
        """
        if not self._positions:
            logger.warning("No BoxTops loaded, cannot start detection")
            return

        position_index = 0

        while True:
            if position_index >= len(self._positions):
                if self.loop:
                    position_index = 0
                    logger.info("Looping recording")
                else:
                    logger.info("Recording finished")
                    break

            yield self._positions[position_index]
            position_index += 1

    def detect_single(self) -> Optional[BoxTop]:
        """
        Detect a single box top (returns the first BoxTop from the recording).

        Returns:
            BoxTop object or None if no BoxTops available
        """
        if self._positions:
            return self._positions[0]
        return None


if __name__ == "__main__":
    # Test the mock detector
    from pprint import pprint

    mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt")

    try:
        count = 0
        for box_position in mock_detector.detect_box_tops():
            if box_position is not None:
                pprint(box_position)
                count += 1
                if count >= 5:  # Only show first 5 for testing
                    break
    except KeyboardInterrupt:
        logger.info("Stopping mock detection...")
