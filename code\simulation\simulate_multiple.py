from math import inf
from packer_bullet import Bullet<PERSON>acker
from packer_help_functions import create_random_boxes
from pallet import Pallet

n_simulations = 7
lowest_vol = inf
highest_vol = 0
vols = []
times = []
times_per_box = []


def average(list):
    return sum(list) / len(list)


for i in range(n_simulations):
    random_que = create_random_boxes(115, (0.1, 0.6), (0.1, 0.4), (0.1, 0.4), int=False)
    box_queue = random_que
    buffer_size = 4
    pallet = Pallet(1.22, 1.27, 0.1, 1.1)
    p = BulletPacker(pallet)
    vol, time = p.pack_boxes(
        p.pallet, queue=box_queue, buffer_size=buffer_size, disable_prints=True
    )

    times.append(time)
    times_per_box.append(time / len(pallet.packed_boxes))
    vols.append(vol)
    print(f"VOLUME: {vol}%")
    print(f"simulation {i + 1}/{n_simulations} done")


print("\n")
print("------------------")
print(f"NUMBER OF SIMS: {n_simulations}")
print(f"HIGHEST VOLUME: {max(vols)}%")
print(f"LOWEST VOLUME: {min(vols)}%")
print(f"AVERAGE VOLUME: {round(average(vols), 2)}%")
print(f"LONGEST SIMULATION: {round(max(times), 2)}s")
print(f"SHORTEST SIMULATION: {round(min(times), 2)}s")
print(f"AVERAGE TIME: {round(average(times), 2)}s")
print(f"AVERAGE TIME PER BOX: {round(average(times_per_box), 2)}s")
