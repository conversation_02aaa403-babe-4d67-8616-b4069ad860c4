from packing.protocols import Box3<PERSON>, Crate, PlacedBox, Pose
from packing.scores.overhang_score import OverhangScore
from packing.simulator_bullet import BulletPhysicsSimulator


def test_overhang_penalizes_unsupported_area():
    crate = Crate(1.0, 1.0, 1.0)
    sim = BulletPhysicsSimulator()
    # Candidate settled pose above a tiny support
    cand = PlacedBox(Box3D(0.4, 0.2, 0.2), Pose(0.0, 0.0, 0.2, 0.0))
    support = PlacedBox(Box3D(0.1, 0.1, 0.2), Pose(0.0, 0.0, 0.0, 0.0))
    sim.world_reset(crate, [support])
    # Simulate once to prime candidate_metrics and ensure Bullet world present
    settled = sim.simulate_drop(crate, cand)
    assert settled is not None
    s = OverhangScore(weight=1.0, grid_resolution=0.05).score(crate, sim, settled, [support])
    assert s < 0.0


def test_overhang_with_yaw():
    crate = Crate(1.0, 1.0, 1.0)
    sim = BulletPhysicsSimulator()
    support = PlacedBox(Box3D(0.1, 0.4, 0.2), Pose(0.0, 0.0, 0.0, 0.0))
    sim.world_reset(crate, [support])
    cand = PlacedBox(Box3D(0.2, 0.4, 0.2), Pose(0.0, 0.0, 0.2, 1.5707963267948966))  # 90 deg yaw
    settled = sim.simulate_drop(crate, cand)
    assert settled is not None
    s = OverhangScore(weight=1.0, grid_resolution=0.05).score(crate, sim, settled, [support])
    assert s < 0.0
