from dataclasses import dataclass
from typing import Iterable

from ..protocols import Crate, PhysicsSimulatorProtocol, PlacedBox, ScoringHeuristicProtocol


@dataclass
class GapScore(ScoringHeuristicProtocol):
    weight: float = 1.0
    target_gap: float = 0.015

    def score(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
    ) -> float:
        metrics = getattr(simulator, "candidate_metrics", lambda: None)()
        if metrics is None:
            return -1e9
        face_dist = metrics["face_dist"]
        total = 0.0
        for dist in face_dist:
            ad = abs(dist)
            if ad < self.target_gap:
                return -1e6  # hard penalty for too small gaps
            total += (1.0 / (ad + 1.0)) ** 2 + (ad / 2.0) ** 2
        return self.weight * total
