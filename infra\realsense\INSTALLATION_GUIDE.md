# RealSense SDK 2.0 Installation Guide for Ubuntu 24.04

This guide provides step-by-step instructions to install Intel RealSense SDK 2.0 and set up Python bindings on Ubuntu 24.04.

## Prerequisites

- Ubuntu 24.04 LTS
- Intel RealSense camera (D400/D500 series)
- Internet connection
- sudo privileges

## Step 1: Update System and Install Dependencies

```bash
# Update system packages
sudo apt-get update && sudo apt-get upgrade -y

# Install core build dependencies
sudo apt install git wget cmake build-essential libssl-dev libusb-1.0-0-dev libudev-dev pkg-config libgtk-3-dev -y

# Install additional dependencies for OpenGL support
sudo apt install libglfw3-dev libgl1-mesa-dev libglu1-mesa-dev -y

# Install v4l-utils for camera support
sudo apt install v4l-utils -y

# Install Python development packages
sudo apt install python3-dev python3-full pipenv -y
```

## Step 2: Clone and Build RealSense SDK

```bash
# Create working directory
mkdir -p /home/<USER>/realsense
cd /home/<USER>/realsense

# Clone the latest RealSense SDK
git clone https://github.com/IntelRealSense/librealsense.git
cd librealsense

# Set up udev rules for device permissions
./scripts/setup_udev_rules.sh

# Create build directory
mkdir build && cd build

# Configure build with CMake
cmake ../ -DCMAKE_BUILD_TYPE=Release -DBUILD_EXAMPLES=true

# Build the SDK (use all available cores)
make -j$(nproc)

# Install the SDK
sudo make install

# Update library cache
sudo ldconfig
```

## Step 3: Set Up Python Environment

```bash
# Create Python project directory
mkdir -p /home/<USER>/realsense_python
cd /home/<USER>/realsense_python

# Initialize Pipenv environment with required packages
pipenv install numpy opencv-python pybind11 cython
```

## Step 4: Build Python Bindings

```bash
# Create separate build directory for Python bindings
cd /home/<USER>/realsense/librealsense
mkdir build_python && cd build_python

# Configure build with Python bindings enabled
cmake ../ -DCMAKE_BUILD_TYPE=Release -DBUILD_PYTHON_BINDINGS=true -DPYTHON_EXECUTABLE=/home/<USER>/.local/share/virtualenvs/realsense_python-um8dQ9Ew/bin/python

# Build Python bindings
make -j$(nproc) pyrealsense2

# Install with sudo (includes Python module installation)
sudo make install
```

## Step 5: Copy Python Module to Virtual Environment

```bash
# Copy the built Python module to the virtual environment
cp /home/<USER>/realsense/librealsense/build_python/Release/pyrealsense2.cpython-312-x86_64-linux-gnu.so.2.55.1 /home/<USER>/.local/share/virtualenvs/realsense_python-um8dQ9Ew/lib/python3.12/site-packages/pyrealsense2.so

# Update library cache again
sudo ldconfig
```

## Step 6: Test Installation

Create a test script to verify the installation:

```python
#!/usr/bin/env python3
"""
Test script to verify pyrealsense2 installation
"""

try:
    import pyrealsense2 as rs
    print("✓ pyrealsense2 imported successfully!")
    
    # Check version
    print(f"RealSense library version: {rs.__version__}")
    
    # Try to create a context
    ctx = rs.context()
    print("✓ Context created successfully!")
    
    # Check for connected devices
    devices = ctx.query_devices()
    print(f"Number of connected devices: {len(devices)}")
    
    if len(devices) > 0:
        for i, device in enumerate(devices):
            print(f"Device {i}: {device.get_info(rs.camera_info.name)}")
    else:
        print("No RealSense devices detected. Connect a device to test streaming.")
        
except ImportError as e:
    print(f"✗ Error importing pyrealsense2: {e}")
except Exception as e:
    print(f"✗ Error: {e}")
```

Run the test:

```bash
cd /home/<USER>/realsense_python
pipenv run python test_pyrealsense.py
```

Expected output:
```
✓ pyrealsense2 imported successfully!
RealSense library version: 2.55.1
✓ Context created successfully!
Number of connected devices: 1
Device 0: Intel RealSense D455
```

## Step 7: Install Example Scripts

The repository includes two example scripts:

1. **Basic Pointcloud Example** (`pointcloud_basic.py`)
2. **Advanced Temporal Filtering Example** (`pointcloud_temporal_filter.py`)

Run examples:

```bash
# Basic pointcloud example
pipenv run python pointcloud_basic.py

# Advanced example with temporal filtering
pipenv run python pointcloud_temporal_filter.py --enable-temporal --enable-spatial --enable-hole-filling
```

## Troubleshooting

### Common Issues and Solutions

1. **Permission denied errors**:
   ```bash
   # Make sure udev rules are properly installed
   sudo ./scripts/setup_udev_rules.sh
   # Restart your system or reconnect the camera
   ```

2. **No device found**:
   ```bash
   # Check if device is recognized by the system
   lsusb | grep Intel
   # Check RealSense viewer
   realsense-viewer
   ```

3. **CMake configuration fails**:
   ```bash
   # Install missing dependencies
   sudo apt install libssl-dev libusb-1.0-0-dev libudev-dev pkg-config
   ```

4. **Python import errors**:
   ```bash
   # Verify Python module location
   find /home/<USER>/.local/share/virtualenvs/realsense_python-*/lib/python3.12/site-packages/ -name "*pyrealsense*"
   ```

5. **Build errors**:
   ```bash
   # Clean build directory and rebuild
   cd /home/<USER>/realsense/librealsense/build
   make clean
   make -j$(nproc)
   ```

## File Structure

After installation, your directory structure should look like:

```
/home/<USER>/realsense/
├── librealsense/                    # RealSense SDK source code
│   ├── build/                       # C++ SDK build directory
│   ├── build_python/                # Python bindings build directory
│   └── scripts/                     # Installation scripts
└── realsense_python/                # Python project directory
    ├── pointcloud_basic.py          # Basic pointcloud example
    ├── pointcloud_temporal_filter.py # Advanced filtering example
    ├── test_pyrealsense.py          # Installation test script
    ├── README.md                    # Usage guide
    ├── INSTALLATION_GUIDE.md        # This file
    ├── Pipfile                      # Pipenv configuration
    └── Pipfile.lock                 # Pipenv lock file
```

## Key Components Installed

- **RealSense SDK 2.55.1** - Core C++ library
- **pyrealsense2** - Python bindings
- **RealSense Viewer** - GUI application for camera testing
- **Example applications** - Various C++ examples
- **Python examples** - Pointcloud generation scripts

## Notes

- This installation was tested on Ubuntu 24.04 with kernel 6.11.0-29-generic
- The kernel patches for enhanced camera support are not available for Ubuntu 24.04 yet, but the SDK works without them for most use cases
- The Python bindings are built from source since pre-built packages are not available for Ubuntu 24.04
- Using Pipenv is recommended to avoid conflicts with system Python packages

## Additional Resources

- [RealSense Developer Documentation](https://dev.realsenseai.com/)
- [RealSense GitHub Repository](https://github.com/IntelRealSense/librealsense)
- [RealSense Python Examples](https://github.com/IntelRealSense/librealsense/tree/master/wrappers/python/examples)

---

Installation completed successfully on: Ubuntu 24.04 LTS, RealSense SDK 2.55.1, Python 3.12.3
